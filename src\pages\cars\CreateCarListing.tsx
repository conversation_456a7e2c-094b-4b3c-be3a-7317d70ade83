import { useState } from "react";
import { useNavigate } from "react-router-dom";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import { useAuth } from "@/components/layout/AuthProvider";
import { supabase } from "@/integrations/supabase/client";
import { useToast } from "@/hooks/use-toast";
import { useQueryClient } from "@tanstack/react-query";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Separator } from "@/components/ui/separator";
import FeatureSelector from "@/components/features/FeatureSelector";
import { LocationPicker } from "@/components/maps/LocationPicker";
import { LocationData } from "@/lib/google-maps";
import { Database } from "@/integrations/supabase/types";
import ImageUploader from "@/components/ui/ImageUploader";
import { Loader2 } from "lucide-react";
import { parsePrice } from "@/lib/price-utils";

type CarType = Database["public"]["Enums"]["car_type"];

const CreateCarListing = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();

  const [formData, setFormData] = useState({
    title: "",
    description: "",
    car_type: "" as CarType,
    make: "",
    model: "",
    year: new Date().getFullYear(),
    location: "",
    price_day: 0,
    price_week: 0,
    price_month: 0,
    seats: 5,
    transmission: "automatic",
    fuel_type: "petrol",
    features: [],
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(
    null
  );
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [thumbnailIndex, setThumbnailIndex] = useState<number>(0);

  const handleChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleNumberChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({
      ...prev,
      [name]: parsePrice(value),
    }));
  };

  const handleFeaturesChange = (features: string[]) => {
    setFormData((prev) => ({
      ...prev,
      features,
    }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!user) {
      toast({
        title: "Authentication required",
        description: "Please sign in to create a car listing",
        variant: "destructive",
      });
      navigate("/auth");
      return;
    }

    // Validation
    if (!formData.title) {
      toast({
        title: "Error",
        description: "Please enter a title",
        variant: "destructive",
      });
      return;
    }

    if (!formData.description) {
      toast({
        title: "Error",
        description: "Please enter a description",
        variant: "destructive",
      });
      return;
    }

    if (!formData.car_type) {
      toast({
        title: "Error",
        description: "Please select a car type",
        variant: "destructive",
      });
      return;
    }

    if (!formData.make || !formData.model) {
      toast({
        title: "Error",
        description: "Please enter make and model",
        variant: "destructive",
      });
      return;
    }

    if (!selectedLocation) {
      toast({
        title: "Error",
        description: "Please select a location on the map",
        variant: "destructive",
      });
      return;
    }

    if (selectedImages.length === 0) {
      toast({
        title: "Error",
        description: "Please upload at least one image of your car",
        variant: "destructive",
      });
      return;
    }

    // Validate prices
    if (formData.price_day <= 0) {
      toast({
        title: "Error",
        description: "Daily price must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    if (formData.price_week <= 0) {
      toast({
        title: "Error",
        description: "Weekly price must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    if (formData.price_month <= 0) {
      toast({
        title: "Error",
        description: "Monthly price must be greater than 0",
        variant: "destructive",
      });
      return;
    }

    try {
      setIsSubmitting(true);

      // First, ensure user has a valid profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("id", user.id)
        .single();

      if (profileError || !profile) {
        console.error("Profile check failed:", profileError);

        // Try to create profile if it doesn't exist
        const { error: createProfileError } = await supabase.rpc(
          "handle_new_user_manual",
          {
            user_id: user.id,
            first_name: user.user_metadata?.first_name || "",
            last_name: user.user_metadata?.last_name || "",
            email: user.email || user.user_metadata?.email || "",
            phone_number: user.phone || user.user_metadata?.phone_number || "",
          }
        );

        if (createProfileError) {
          console.error("Failed to create profile:", createProfileError);
          toast({
            title: "Profile Error",
            description:
              "Unable to create your profile. Please contact support.",
            variant: "destructive",
          });
          return;
        }
      }

      // Upload images if provided
      let imageUrls: string[] = [];
      if (selectedImages.length > 0) {
        const uploadPromises = selectedImages.map(async (file: File) => {
          const fileExt = file.name.split(".").pop();
          const fileName = `${Math.random()}.${fileExt}`;
          const filePath = `cars/${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from("car-images")
            .upload(filePath, file);

          if (uploadError) throw uploadError;

          const {
            data: { publicUrl },
          } = supabase.storage.from("car-images").getPublicUrl(filePath);

          return publicUrl;
        });

        imageUrls = await Promise.all(uploadPromises);

        // Reorder images to put thumbnail first
        if (thumbnailIndex > 0 && thumbnailIndex < imageUrls.length) {
          const thumbnailUrl = imageUrls[thumbnailIndex];
          imageUrls.splice(thumbnailIndex, 1);
          imageUrls.unshift(thumbnailUrl);
        }
      }

      const { error } = await supabase.from("cars").insert({
        ...formData,
        latitude: selectedLocation?.latitude || null,
        longitude: selectedLocation?.longitude || null,
        formatted_address: selectedLocation?.formatted_address || null,
        owner_id: user.id,
        images: imageUrls,
        status: "pending", // Explicitly set to pending for admin approval
      });

      if (error) throw error;

      toast({
        title: "Car Listing Submitted Successfully!",
        description:
          "Your car has been submitted for admin approval. You'll be notified once it's reviewed and approved.",
        duration: 5000, // Auto-dismiss after 5 seconds
      });

      // Invalidate listing caches to reflect new listing
      queryClient.invalidateQueries({ queryKey: ["cars"] });

      // Navigate back to where user came from, or default to cars
      const referrer = document.referrer;
      if (referrer.includes("/host")) {
        navigate("/host/listings");
      } else {
        navigate("/cars");
      }
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Failed to create car listing",
        variant: "destructive",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />

      <div className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold mb-2">List Your Car</h1>
        <p className="text-muted-foreground mb-8">
          Share your car with travelers and earn extra income
        </p>

        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Car Details</CardTitle>
            <CardDescription>
              Provide information about your car to attract renters
            </CardDescription>
            <div className="mt-4 p-3 bg-warm-tan/10 border border-warm-tan/30 rounded-lg">
              <p className="text-sm text-dark-brown">
                <strong>📋 Approval Process:</strong> Your car listing will be
                reviewed by our admin team before going live. This typically
                takes 24-48 hours.
              </p>
            </div>
          </CardHeader>
          <CardContent>
            <form onSubmit={handleSubmit} className="space-y-6">
              <div className="space-y-4">
                <div className="grid gap-2">
                  <Label htmlFor="title">Title</Label>
                  <Input
                    id="title"
                    name="title"
                    placeholder="E.g., Luxury Mercedes SUV for Rent"
                    value={formData.title}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="grid gap-2">
                  <Label htmlFor="description">Description</Label>
                  <Textarea
                    id="description"
                    name="description"
                    placeholder="Describe your car, its condition, special features..."
                    value={formData.description}
                    onChange={handleChange}
                    rows={5}
                    required
                  />
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="make">Make</Label>
                    <Input
                      id="make"
                      name="make"
                      placeholder="E.g., Toyota"
                      value={formData.make}
                      onChange={handleChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="model">Model</Label>
                    <Input
                      id="model"
                      name="model"
                      placeholder="E.g., Corolla"
                      value={formData.model}
                      onChange={handleChange}
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="year">Year</Label>
                    <Input
                      id="year"
                      name="year"
                      type="number"
                      value={formData.year}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="car_type">Car Type</Label>
                    <Select
                      value={formData.car_type}
                      onValueChange={(value) =>
                        setFormData((prev) => ({
                          ...prev,
                          car_type: value as CarType,
                        }))
                      }
                    >
                      <SelectTrigger id="car_type">
                        <SelectValue placeholder="Select car type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="sedan">Sedan</SelectItem>
                        <SelectItem value="suv">SUV</SelectItem>
                        <SelectItem value="luxury">Luxury</SelectItem>
                        <SelectItem value="compact">Compact</SelectItem>
                        <SelectItem value="convertible">Convertible</SelectItem>
                        <SelectItem value="van">Van</SelectItem>
                        <SelectItem value="truck">Truck</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <Separator />

                <div className="grid gap-2">
                  <Label htmlFor="location">Location</Label>
                  <Input
                    id="location"
                    name="location"
                    placeholder="E.g., Banjul, Serrekunda"
                    value={formData.location}
                    onChange={handleChange}
                    required
                  />
                </div>

                <div className="space-y-2">
                  <Label>Car Location on Map</Label>
                  <LocationPicker
                    onLocationSelect={(location) => {
                      setSelectedLocation(location);
                      // Never override manually entered location text
                      // Only update if the location field is completely empty
                      if (
                        !formData.location ||
                        formData.location.trim() === ""
                      ) {
                        setFormData((prev) => ({
                          ...prev,
                          location:
                            location.formatted_address ||
                            `${location.latitude}, ${location.longitude}`,
                        }));
                      }
                    }}
                    initialLocation={selectedLocation || undefined}
                    height="400px"
                    showSearch={true}
                    showCurrentLocationButton={true}
                  />
                  {selectedLocation && (
                    <p className="text-sm text-gray-600">
                      Selected: {selectedLocation.formatted_address}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="price_day">Price per Day ($)</Label>
                    <Input
                      id="price_day"
                      name="price_day"
                      type="number"
                      min="0"
                      value={formData.price_day}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="price_week">Price per Week ($)</Label>
                    <Input
                      id="price_week"
                      name="price_week"
                      type="number"
                      min="0"
                      value={formData.price_week}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="price_month">Price per Month ($)</Label>
                    <Input
                      id="price_month"
                      name="price_month"
                      type="number"
                      min="0"
                      value={formData.price_month}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                </div>

                <Separator />

                <div className="grid grid-cols-3 gap-4">
                  <div className="grid gap-2">
                    <Label htmlFor="seats">Number of Seats</Label>
                    <Input
                      id="seats"
                      name="seats"
                      type="number"
                      min="1"
                      max="15"
                      value={formData.seats}
                      onChange={handleNumberChange}
                      required
                    />
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="transmission">Transmission</Label>
                    <Select
                      value={formData.transmission}
                      onValueChange={(value) =>
                        setFormData((prev) => ({
                          ...prev,
                          transmission: value,
                        }))
                      }
                    >
                      <SelectTrigger id="transmission">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="automatic">Automatic</SelectItem>
                        <SelectItem value="manual">Manual</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="grid gap-2">
                    <Label htmlFor="fuel_type">Fuel Type</Label>
                    <Select
                      value={formData.fuel_type}
                      onValueChange={(value) =>
                        setFormData((prev) => ({ ...prev, fuel_type: value }))
                      }
                    >
                      <SelectTrigger id="fuel_type">
                        <SelectValue placeholder="Select type" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="petrol">Petrol</SelectItem>
                        <SelectItem value="diesel">Diesel</SelectItem>
                        <SelectItem value="electric">Electric</SelectItem>
                        <SelectItem value="hybrid">Hybrid</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>
              </div>

              <Separator />

              <FeatureSelector
                type="car"
                selectedFeatures={formData.features}
                onFeaturesChange={handleFeaturesChange}
              />

              <div>
                <label className="text-sm font-medium">Car Images</label>
                <ImageUploader
                  onImagesChange={setSelectedImages}
                  onThumbnailChange={setThumbnailIndex}
                  maxImages={10}
                  className="mt-2"
                />
              </div>

              <Button
                type="submit"
                className="w-full bg-accent hover:bg-accent/90"
                disabled={isSubmitting}
              >
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Creating Listing...
                  </>
                ) : (
                  "Create Car Listing"
                )}
              </Button>
            </form>
          </CardContent>
        </Card>
      </div>

      <Footer />
    </div>
  );
};

export default CreateCarListing;
