import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { supabase } from "@/integrations/supabase/client";
import { useAuth } from "@/components/layout/AuthProvider";
import { useQueryClient } from "@tanstack/react-query";
import { useToast } from "@/hooks/use-toast";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import Navbar from "@/components/layout/Navbar";
import Footer from "@/components/layout/Footer";
import FeatureSelector from "@/components/features/FeatureSelector";
import { LocationPicker } from "@/components/maps/LocationPicker";
import { LocationData } from "@/lib/google-maps";
import ImageUploader from "@/components/ui/ImageUploader";
import { Loader2 } from "lucide-react";
import { parsePrice } from "@/lib/price-utils";

const formSchema = z.object({
  title: z.string().min(1, "Title is required"),
  description: z.string().min(10, "Description must be at least 10 characters"),
  location: z.string().min(1, "Location is required"),
  price: z.number().min(1, "Price must be greater than 0"),
  beds: z.number().min(1, "Number of beds must be at least 1"),
  baths: z.number().min(1, "Number of baths must be at least 1"),
});

type CreateListingForm = z.infer<typeof formSchema>;

type CreateListingFormData = CreateListingForm & {
  features: string[];
};

const CreateListing = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([]);
  const [selectedLocation, setSelectedLocation] = useState<LocationData | null>(
    null
  );
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [thumbnailIndex, setThumbnailIndex] = useState<number>(0);

  const form = useForm<CreateListingForm>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      price: 0,
      beds: 1,
      baths: 1,
    },
  });

  const onSubmit = async (data: CreateListingForm) => {
    if (!user) {
      toast({
        variant: "destructive",
        title: "Authentication Required",
        description: "Please sign in to create a listing.",
      });
      return;
    }

    // Additional validation for required fields
    if (!selectedLocation) {
      toast({
        variant: "destructive",
        title: "Location Required",
        description: "Please select a location on the map.",
      });
      return;
    }

    if (selectedImages.length === 0) {
      toast({
        variant: "destructive",
        title: "Images Required",
        description: "Please upload at least one image of your property.",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // First, ensure user has a valid profile
      const { data: profile, error: profileError } = await supabase
        .from("profiles")
        .select("id, first_name, last_name")
        .eq("id", user.id)
        .single();

      if (profileError || !profile) {
        console.error("Profile check failed:", profileError);

        // Try to create profile if it doesn't exist
        const { error: createProfileError } = await supabase.rpc(
          "handle_new_user_manual",
          {
            user_id: user.id,
            first_name: user.user_metadata?.first_name || "",
            last_name: user.user_metadata?.last_name || "",
            email: user.email || user.user_metadata?.email || "",
            phone_number: user.phone || user.user_metadata?.phone_number || "",
          }
        );

        if (createProfileError) {
          console.error("Failed to create profile:", createProfileError);
          toast({
            variant: "destructive",
            title: "Profile Error",
            description:
              "Unable to create your profile. Please contact support.",
          });
          return;
        }
      }

      const imageUrls: string[] = [];

      // Upload images first
      if (selectedImages.length > 0) {
        for (let i = 0; i < selectedImages.length; i++) {
          const file = selectedImages[i];
          const fileExt = file.name.split(".").pop();
          const fileName = `${Math.random()}.${fileExt}`;
          const filePath = `${fileName}`;

          const { error: uploadError } = await supabase.storage
            .from("property-images")
            .upload(filePath, file);

          if (uploadError) {
            console.error("Image upload error:", uploadError);
            throw new Error(
              `Failed to upload image ${i + 1}: ${uploadError.message}`
            );
          }

          const { data: urlData } = supabase.storage
            .from("property-images")
            .getPublicUrl(filePath);

          imageUrls.push(urlData.publicUrl);
        }

        // Reorder images to put thumbnail first
        if (thumbnailIndex > 0 && thumbnailIndex < imageUrls.length) {
          const thumbnailUrl = imageUrls[thumbnailIndex];
          imageUrls.splice(thumbnailIndex, 1);
          imageUrls.unshift(thumbnailUrl);
        }
      }

      // Create the property listing
      const listingData = {
        title: data.title,
        description: data.description,
        location: data.location,
        latitude: selectedLocation?.latitude || null,
        longitude: selectedLocation?.longitude || null,
        formatted_address: selectedLocation?.formatted_address || null,
        price: data.price,
        beds: data.beds,
        baths: data.baths,
        owner_id: user.id,
        images: imageUrls,
        features: selectedFeatures,
      };

      const { error, data: insertedData } = await supabase
        .from("properties")
        .insert(listingData)
        .select();

      if (error) {
        console.error("Database insert error:", error);
        throw error;
      }

      toast({
        title: "Listing Submitted Successfully!",
        description:
          "Your property has been submitted for admin approval. You'll be notified once it's reviewed and approved.",
        duration: 5000, // Auto-dismiss after 5 seconds
      });

      // Invalidate listing caches to reflect new listing
      queryClient.invalidateQueries({ queryKey: ["properties"] });
      queryClient.invalidateQueries({ queryKey: ["featured-properties"] });

      // Navigate back to where user came from, or default to listings
      const referrer = document.referrer;
      if (referrer.includes("/host")) {
        navigate("/host/listings");
      } else {
        navigate("/listings");
      }
    } catch (error: any) {
      console.error("Listing creation error:", error);

      let errorMessage = "Something went wrong. Please try again.";

      if (error?.message) {
        if (error.message.includes("foreign key constraint")) {
          errorMessage = "Account setup incomplete. Please contact support.";
        } else if (error.message.includes("permission denied")) {
          errorMessage =
            "You don't have permission to create listings. Please verify your account.";
        } else if (error.message.includes("upload")) {
          errorMessage = error.message;
        } else {
          errorMessage = error.message;
        }
      }

      toast({
        variant: "destructive",
        title: "Error",
        description: errorMessage,
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="min-h-screen bg-white">
      <Navbar />
      <main className="container mx-auto px-4 py-8">
        <h1 className="text-3xl font-bold text-brown mb-2">
          List Your Property
        </h1>
        <p className="text-muted-foreground mb-8">
          Share your property with travelers and earn extra income
        </p>

        <Card className="max-w-3xl mx-auto">
          <CardHeader>
            <CardTitle>Property Details</CardTitle>
            <CardDescription>
              Provide information about your property to attract guests
            </CardDescription>
            <div className="mt-4 p-3 bg-warm-tan/10 border border-warm-tan/30 rounded-lg">
              <p className="text-sm text-dark-brown">
                <strong>📋 Approval Process:</strong> Your listing will be
                reviewed by our admin team before going live. This typically
                takes 24-48 hours.
              </p>
            </div>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="title"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Title</FormLabel>
                      <FormControl>
                        <Input
                          placeholder="Beachfront Villa in Kololi"
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="description"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Description</FormLabel>
                      <FormControl>
                        <Textarea
                          placeholder="Describe your property..."
                          {...field}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="location"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Location</FormLabel>
                      <FormControl>
                        <Input placeholder="Kololi Beach" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <div className="space-y-2">
                  <FormLabel>Property Location on Map</FormLabel>
                  <LocationPicker
                    onLocationSelect={(location) => {
                      setSelectedLocation(location);
                      // Never override manually entered location text
                      // Only update if the location field is completely empty
                      const currentLocation = form.getValues("location");
                      if (!currentLocation || currentLocation.trim() === "") {
                        form.setValue(
                          "location",
                          location.formatted_address ||
                            `${location.latitude}, ${location.longitude}`
                        );
                      }
                    }}
                    initialLocation={selectedLocation || undefined}
                    height="400px"
                    showSearch={true}
                    showCurrentLocationButton={true}
                  />
                  {selectedLocation && (
                    <p className="text-sm text-gray-600">
                      Selected: {selectedLocation.formatted_address}
                    </p>
                  )}
                </div>

                <div className="grid grid-cols-2 gap-4">
                  <FormField
                    control={form.control}
                    name="price"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Price per night ($)</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="0"
                            {...field}
                            onChange={(e) =>
                              field.onChange(parsePrice(e.target.value))
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="beds"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bedrooms</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value) || 1)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="baths"
                    render={({ field }) => (
                      <FormItem>
                        <FormLabel>Bathrooms</FormLabel>
                        <FormControl>
                          <Input
                            type="number"
                            min="1"
                            {...field}
                            onChange={(e) =>
                              field.onChange(Number(e.target.value) || 1)
                            }
                          />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </div>

                <div>
                  <label className="text-sm font-medium">Property Images</label>
                  <ImageUploader
                    onImagesChange={setSelectedImages}
                    onThumbnailChange={setThumbnailIndex}
                    maxImages={10}
                    className="mt-2"
                  />
                </div>

                <Separator />

                <FeatureSelector
                  type="property"
                  selectedFeatures={selectedFeatures}
                  onFeaturesChange={setSelectedFeatures}
                />

                <Button
                  type="submit"
                  className="w-full bg-accent hover:bg-accent/90"
                  disabled={isSubmitting}
                >
                  {isSubmitting ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Creating listing...
                    </>
                  ) : (
                    "Create Listing"
                  )}
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </main>
      <Footer />
    </div>
  );
};

export default CreateListing;
