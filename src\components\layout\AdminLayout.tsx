import { useEffect } from "react";
import { useNavigate, Link, Outlet } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { useAdminAuth } from "./AdminAuthProvider";
import Navbar from "@/components/layout/Navbar";
import {
  LayoutDashboard,
  Users,
  Home,
  FileText,
  CreditCard,
  BarChart,
  Settings,
  LogOut,
} from "lucide-react";

const AdminLayout = () => {
  const navigate = useNavigate();
  const { isAuthenticated, loading, signOut } = useAdminAuth();

  useEffect(() => {
    if (!loading && !isAuthenticated) {
      navigate("/admin/auth");
    }
  }, [isAuthenticated, loading, navigate]);

  const handleSignOut = async () => {
    await signOut();
    navigate("/admin/auth");
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-gray-800"></div>
      </div>
    );
  }

  if (!isAuthenticated) {
    return null;
  }

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {/* Standard Navbar */}
      <Navbar />

      <div className="flex-1 flex">
        {/* Fixed Left Sidebar - Responsive like Host Layout */}
        <aside className="fixed left-0 top-16 h-[calc(100vh-4rem)] w-16 lg:w-64 bg-gray-900 text-white shadow-lg border-r border-gray-700 z-40 transition-all duration-300 ease-in-out">
          <nav className="h-full flex flex-col">
            {/* Brand Section */}
            <div className="p-4 border-b border-gray-700">
              <div className="flex items-center">
                <div className="w-8 h-8 bg-gradient-to-br from-blue-600 to-purple-600 rounded-lg flex items-center justify-center flex-shrink-0">
                  <LayoutDashboard className="h-4 w-4 text-white" />
                </div>
                <span className="ml-3 text-lg font-semibold text-white hidden lg:block">
                  Admin Panel
                </span>
              </div>
            </div>

            {/* Navigation Links */}
            <div className="flex-1 py-4">
              <ul className="space-y-1 px-2">
                <li>
                  <Link
                    to="/admin/dashboard"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Dashboard"
                  >
                    <LayoutDashboard size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Dashboard</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/admin/users"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Users"
                  >
                    <Users size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Users</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/admin/listings"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Listings"
                  >
                    <Home size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Listings</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/admin/bookings"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Bookings"
                  >
                    <FileText size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Bookings</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/admin/payments"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Payments"
                  >
                    <CreditCard size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Payments</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/admin/analytics"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Analytics"
                  >
                    <BarChart size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Analytics</span>
                  </Link>
                </li>
                <li>
                  <Link
                    to="/admin/settings"
                    className="flex items-center gap-3 p-3 rounded-lg hover:bg-gray-800 transition-colors group"
                    title="Settings"
                  >
                    <Settings size={20} className="flex-shrink-0" />
                    <span className="hidden lg:block">Settings</span>
                  </Link>
                </li>
              </ul>
            </div>

            {/* Sign Out Button */}
            <div className="p-2 border-t border-gray-700">
              <Button
                variant="ghost"
                className="flex w-full items-center justify-start gap-3 p-3 text-red-400 hover:text-red-300 hover:bg-gray-800 rounded-lg"
                onClick={handleSignOut}
                title="Sign Out"
              >
                <LogOut size={20} className="flex-shrink-0" />
                <span className="hidden lg:block">Sign Out</span>
              </Button>
            </div>
          </nav>
        </aside>

        {/* Main Content */}
        <main className="flex-1 ml-16 lg:ml-64 transition-all duration-300 ease-in-out">
          <div className="p-6">
            <Outlet />
          </div>
        </main>
      </div>
    </div>
  );
};

export default AdminLayout;
